# onetools Windows 自动安装脚本
# 使用方法: 
# PowerShell: iex ((New-Object System.Net.WebClient).DownloadString('https://your-domain.com/install_windows.ps1'))
# 或者: Invoke-WebRequest -Uri "https://your-domain.com/install_windows.ps1" -UseBasicParsing | Invoke-Expression

# 设置错误处理
$ErrorActionPreference = "Stop"

# 配置
$VERSION_URL = "https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/version.json"
$ONETOOLS_HOME = "$env:USERPROFILE\onetools"
$TEMP_DIR = "$env:TEMP\onetools_install"

# 颜色定义
function Print-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Print-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Print-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Print-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查命令是否存在
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 检查系统要求
function Test-Requirements {
    Print-Info "检查系统要求..."
    
    # 检查操作系统
    if ($env:OS -ne "Windows_NT") {
        Print-Error "此脚本仅支持 Windows 系统"
        exit 1
    }
    
    # 检查 PowerShell 版本
    if ($PSVersionTable.PSVersion.Major -lt 3) {
        Print-Error "需要 PowerShell 3.0 或更高版本"
        exit 1
    }
    
    # 检查 .NET Framework (用于解压)
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
    } catch {
        Print-Error "需要 .NET Framework 4.5 或更高版本"
        exit 1
    }
    
    Print-Success "系统要求检查通过"
}

# 获取当前安装的版本
function Get-CurrentVersion {
    if (Test-Command "onetools") {
        try {
            $versionOutput = & onetools version 2>$null
            if ($versionOutput -and $versionOutput -match "^(V[0-9]+(\.[0-9]+)*)") {
                return $matches[1]
            }
        } catch {
            # 忽略错误
        }
    }
    return ""
}

# 获取最新版本信息
function Get-LatestVersionInfo {
    Print-Info "获取最新版本信息..."
    
    try {
        $response = Invoke-WebRequest -Uri $VERSION_URL -UseBasicParsing
        $jsonData = $response.Content | ConvertFrom-Json
        
        $version = $jsonData.version
        $url = $jsonData.url
        
        if (-not $version -or -not $url) {
            Print-Error "版本信息解析失败"
            exit 1
        }
        
        # 从 version 字段提取版本号 (例如: onetools_V1.1.0_8073195.zip -> V1.1.0)
        if ($version -match "(V[0-9.]+)") {
            $script:LATEST_VERSION = $matches[1]
            $script:DOWNLOAD_URL = $url
            
            if (-not $script:LATEST_VERSION) {
                Print-Error "无法解析最新版本号"
                exit 1
            }
            
            Print-Success "最新版本: $script:LATEST_VERSION"
        } else {
            Print-Error "无法解析最新版本号"
            exit 1
        }
    } catch {
        Print-Error "无法获取版本信息"
        exit 1
    }
}

# 添加到 PATH 环境变量
function Add-ToPath {
    param([string]$DirToAdd)
    
    # 检查是否已经在 PATH 中
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $currentSystemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    $fullCurrentPath = "$currentUserPath;$currentSystemPath"
    
    if ($fullCurrentPath -like "*$DirToAdd*") {
        Print-Info "PATH 中已包含 $DirToAdd"
        return
    }
    
    Print-Info "添加 $DirToAdd 到 PATH"
    
    # 添加到用户级 PATH 环境变量
    $newPath = if ($currentUserPath) { "$currentUserPath;$DirToAdd" } else { $DirToAdd }
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    
    # 更新当前会话的 PATH
    $env:PATH = "$env:PATH;$DirToAdd"
    
    Print-Success "已添加 $DirToAdd 到 PATH"
    Print-Info "要立即生效，请重新打开 PowerShell 或命令提示符"
}

# 下载并安装 onetools
function Install-OneTools {
    Print-Info "开始下载和安装 onetools..."
    
    # 创建临时目录
    if (Test-Path $TEMP_DIR) {
        Remove-Item -Path $TEMP_DIR -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TEMP_DIR -Force | Out-Null
    
    try {
        # 下载文件
        $zipFile = Join-Path $TEMP_DIR "onetools.zip"
        Print-Info "正在下载: $script:DOWNLOAD_URL"
        
        Invoke-WebRequest -Uri $script:DOWNLOAD_URL -OutFile $zipFile -UseBasicParsing
        
        # 解压文件
        Print-Info "正在解压文件..."
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $TEMP_DIR)
        
        # 查找解压后的 onetools 目录
        $onetoolsSourceDir = Join-Path $TEMP_DIR "onetools"
        if (-not (Test-Path $onetoolsSourceDir)) {
            Print-Error "未找到解压后的 onetools 目录"
            exit 1
        }
        
        # 验证必要文件存在
        $onetoolsBinary = Join-Path $onetoolsSourceDir "onetools.exe"
        if (-not (Test-Path $onetoolsBinary)) {
            Print-Error "未找到 onetools.exe 可执行文件"
            exit 1
        }
        
        # 创建 onetools 主目录并复制所有文件
        Print-Info "正在安装 onetools 到 $ONETOOLS_HOME..."
        
        # 删除旧的安装目录（如果存在）
        if (Test-Path $ONETOOLS_HOME) {
            Print-Info "删除旧版本..."
            Remove-Item -Path $ONETOOLS_HOME -Recurse -Force
        }
        
        # 创建目录并复制整个 onetools 目录
        $parentDir = Split-Path $ONETOOLS_HOME -Parent
        if (-not (Test-Path $parentDir)) {
            New-Item -ItemType Directory -Path $parentDir -Force | Out-Null
        }
        Copy-Item -Path $onetoolsSourceDir -Destination $ONETOOLS_HOME -Recurse -Force
        
        # 添加到 PATH 环境变量
        Add-ToPath $ONETOOLS_HOME
        
        Print-Success "onetools 已成功安装到:"
        Print-Info "  程序目录: $ONETOOLS_HOME"
        Print-Info "  可执行文件: $(Join-Path $ONETOOLS_HOME 'onetools.exe')"
        
        # 显示安装的内容
        $gradleDir = Join-Path $ONETOOLS_HOME "Gradle"
        if (Test-Path $gradleDir) {
            Print-Info "  Gradle 目录: $gradleDir"
        }
        
    } finally {
        # 清理临时文件
        if (Test-Path $TEMP_DIR) {
            Remove-Item -Path $TEMP_DIR -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
}

# 主函数
function Main {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "       onetools Windows 自动安装脚本" -ForegroundColor Cyan  
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # 检查系统要求
    Test-Requirements
    
    # 获取当前版本
    $currentVersion = Get-CurrentVersion
    if ($currentVersion) {
        Print-Info "当前已安装版本: $currentVersion"
    } else {
        Print-Info "未检测到已安装的 onetools"
    }
    
    # 获取最新版本信息
    Get-LatestVersionInfo
    
    # 比较版本
    if ($script:LATEST_VERSION -eq $currentVersion) {
        Print-Success "当前已是最新版本 ($currentVersion)"
        exit 0
    } else {
        Print-Info "准备安装 onetools $script:LATEST_VERSION"
    }
    
    # 安装 onetools
    Install-OneTools
    
    # 验证安装
    if (Test-Command "onetools") {
        $installedVersion = Get-CurrentVersion
        Print-Success "安装完成! 当前版本: $installedVersion"
        Print-Info "使用 'onetools --help' 查看帮助信息"
    } else {
        Print-Warning "安装验证失败，请重新打开 PowerShell 或命令提示符"
        Print-Info "或运行以下命令:"
        Print-Info "`$env:PATH = `"`$env:PATH;$ONETOOLS_HOME`""
    }
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "           安装完成!" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
}

# 运行主函数
try {
    Main
} catch {
    Print-Error "安装过程中发生错误: $($_.Exception.Message)"
    exit 1
}