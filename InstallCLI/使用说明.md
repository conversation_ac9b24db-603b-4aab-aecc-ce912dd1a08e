# onetools 自动安装脚本使用说明

## 概述

本目录提供了 onetools 工具在 macOS 和 Windows 平台上的自动安装脚本，能够自动检测版本、下载、安装和升级 onetools 命令行工具。

## 文件说明

- `install_macos.sh` - macOS 自动安装脚本
- `install_windows.ps1` - Windows PowerShell 安装脚本 (推荐)
- `install_windows.bat` - Windows 批处理安装脚本 (备选)
- `使用说明.md` - 使用说明

## 快速开始

### macOS 用户

#### 在线安装 (推荐)
```bash
curl -fsSL https://your-domain.com/install_macos.sh | bash
```

#### 本地安装
```bash
# 1. 下载脚本到本地
curl -fsSL -o install_macos.sh https://your-domain.com/install_macos.sh

# 2. 添加执行权限
chmod +x install_macos.sh

# 3. 运行安装
./install_macos.sh
```

### Windows 用户

#### PowerShell 在线安装 (推荐)
在 PowerShell 中运行以下命令：
```powershell
iex ((New-Object System.Net.WebClient).DownloadString('https://your-domain.com/install_windows.ps1'))
```

#### PowerShell 本地安装
```powershell
# 1. 下载脚本
Invoke-WebRequest -Uri "https://your-domain.com/install_windows.ps1" -OutFile "install_windows.ps1"

# 2. 运行安装
powershell -ExecutionPolicy Bypass -File "install_windows.ps1"
```

#### 批处理文件安装
直接双击运行 `install_windows.bat` 文件，或在命令提示符中运行。

## 安装位置

### macOS
- **安装目录**: `~/onetools/` (用户目录)
- **可执行文件**: `~/onetools/onetools`
- **Gradle 目录**: `~/onetools/Gradle/`

### Windows
- **安装目录**: `%USERPROFILE%\onetools\` (用户目录)
- **可执行文件**: `%USERPROFILE%\onetools\onetools.exe`
- **Gradle 目录**: `%USERPROFILE%\onetools\Gradle\`

脚本会自动将安装目录添加到系统 PATH 环境变量中。

## 常见问题

### macOS 问题

**Q: 提示权限不足怎么办？**
A: 脚本会自动使用 sudo 获取管理员权限，按提示输入密码即可。

**Q: 安装后找不到 onetools 命令？**
A: 重新打开终端，或运行 ` source ~/.zshrc `

**Q: curl 证书错误？**
A: 可以使用 `curl -fsSL -k` 跳过证书验证（不推荐）

### Windows 问题

**Q: PowerShell 执行策略限制？**
A: 运行 `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

**Q: 安装后无法使用 onetools 命令？**
A: 重新打开 PowerShell 或命令提示符，环境变量需要重新加载

**Q: 下载失败？**
A: 检查网络连接，或尝试使用代理


## 卸载方法

### macOS
```bash
# 删除安装目录 (无需 sudo)
rm -rf ~/onetools

# 从 shell 配置文件中移除 PATH 配置
# 编辑 ~/.zshrc, ~/.bashrc, 或 ~/.bash_profile，删除包含 onetools 的 PATH 行
```

### Windows
```powershell
# 删除安装目录
Remove-Item -Path "$env:USERPROFILE\onetools" -Recurse -Force

# 从环境变量中移除路径（需要手动编辑环境变量）
```