@echo off
setlocal enabledelayedexpansion

REM onetools Windows 自动安装脚本 (批处理版本)
REM 使用方法: 直接运行此批处理文件

echo ========================================
echo        onetools Windows 自动安装脚本
echo ========================================
echo.

REM 检查 PowerShell 是否可用
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] 检测到 PowerShell，使用 PowerShell 脚本进行安装...
    echo.
    
    REM 获取当前脚本所在目录
    set "SCRIPT_DIR=%~dp0"
    
    REM 检查 PowerShell 脚本是否存在
    if exist "%SCRIPT_DIR%install_windows.ps1" (
        echo [INFO] 运行 PowerShell 安装脚本...
        powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%install_windows.ps1"
        goto :end
    ) else (
        echo [WARNING] 未找到 PowerShell 脚本，请手动下载 install_windows.ps1
        echo [INFO] 或使用在线安装命令：
        echo powershell -ExecutionPolicy Bypass -Command "iex ((New-Object System.Net.WebClient).DownloadString('https://your-domain.com/install_windows.ps1'))"
        goto :error
    )
)

REM 如果 PowerShell 不可用，显示手动安装说明
echo [WARNING] 未检测到 PowerShell，无法自动安装
echo [INFO] 建议安装 PowerShell 以获得更好的安装体验
echo.
echo [INFO] 手动安装步骤：
echo 1. 访问: https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/version.json
echo 2. 查看返回的 JSON 中的 "url" 字段
echo 3. 下载该 URL 指向的 zip 文件
echo 4. 解压到: %USERPROFILE%\onetools
echo 5. 将 %USERPROFILE%\onetools 添加到系统 PATH 环境变量
echo.
echo 或者安装 PowerShell 后运行：
echo powershell -ExecutionPolicy Bypass -File "%~dp0install_windows.ps1"
echo.

goto :end

:error
echo.
echo [ERROR] 安装失败
pause
exit /b 1

:end
echo.
echo ========================================
echo            安装完成!
echo ========================================
echo.
echo 如需帮助，请运行: onetools --help
pause